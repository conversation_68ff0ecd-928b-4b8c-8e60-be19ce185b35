package com.daddylab.supplier.item;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.daddylab.supplier.item.infrastructure.config.batch.reader.mybatisplus.MybatisPlusPagingItemReader;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.batch.core.configuration.annotation.JobBuilderFactory;
import org.springframework.batch.core.configuration.annotation.StepBuilderFactory;
import org.springframework.batch.core.job.builder.JobBuilder;
import org.springframework.batch.core.launch.support.SimpleJobLauncher;
import org.springframework.batch.core.repository.JobRepository;
import org.springframework.batch.core.repository.support.MapJobRepositoryFactoryBean;
import org.springframework.batch.core.repository.support.SimpleJobRepository;
import org.springframework.batch.core.step.builder.StepBuilder;
import org.springframework.dao.DataAccessException;
import org.springframework.retry.backoff.FixedBackOffPolicy;

import java.io.IOException;
import java.util.ArrayList;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;

@Slf4j
public class SpringBatchTests {
    
    @Test
    public void testMybatisPlusPagingItemReader () throws Exception {
        MybatisPlusPagingItemReader<Integer> mybatisPlusPagingItemReader = new MybatisPlusPagingItemReader<>();
        mybatisPlusPagingItemReader.setPageSize(100);
        AtomicInteger id = new AtomicInteger();
        final int total = 10000;
        mybatisPlusPagingItemReader.setFunction(new Function<IPage<Integer>, IPage<Integer>>() {
            @Override
            public IPage<Integer> apply(IPage<Integer> integerIPage) {
                log.info("正在同步 {}", integerIPage.getCurrent());
                Page<Integer> integerPage = new Page<>(integerIPage.getCurrent(), integerIPage.getSize(), total);
                ArrayList<Integer> records = new ArrayList<>();
                if (id.get() >= total) {
                    return null;
                }
                for (int i = 0; i < 100; i++) {
                    int incremented = id.incrementAndGet();
                    records.add(incremented);
                    if (incremented >= total) {
                        break;
                    }
                }
                integerPage.setRecords(records);
                return integerPage;
            }
        });
      
    }
}
